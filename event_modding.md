# Event Modding - Victoria 3

Event modding is an essential part of creating engaging and immersive mods in Victoria 3. It allows modders to add flavor, personalities, and interactive elements to the game. Events can be used to drive the history, mechanics, and lore of your mod. This document provides a comprehensive guide to creating and managing events within your mod.

## Contents

1. **File Location**
   - Describes where to store event files and recommended directory structures for proper organization.

2. **File Structure**
   - Guidelines on organizing event scripts, including namespaces and commenting.

3. **Event Structure**
   - Explanation of the basic structure of an event, including ID, type, placement, and other essential attributes.

4. **Hidden Events**
   - How to create events that do not require user interaction but are essential for executing background effects.

5. **Event Templates**
   - Provides sample templates for both standard and hidden events, allowing for quick start and consistency.

## Key Elements of Event Modding

### Namespace
- **Namespace**: A crucial aspect of event definition used for uniquely identifying each event within your mod. Events are named and numbered using this namespace.

### Main Event Attributes
- **ID, Type, and Placement**: The core identifiers of an event, defining its scope and location.
- **Title, Description, and Flavor**: How the event is presented to the player, along with its narrative context.

### Event Execution
- **Trigger**: Defines the conditions under which an event will occur.
- **Immediate Effects**: Actions executed immediately when an event is triggered before any player interaction.
- **Options and Outcomes**: Choices presented to the player and their resulting effects, if any.

## Usage of Tools and Variables

- Detailed guidance on using tools like scripting variables, scopes, cooldowns, and more to create complex and nuanced events.

---

*Retrieved from: [Event Modding Wiki](https://vic3.paradoxwikis.com/Event_modding)*

**Note**: Make sure all event scripts are encoded in UTF-8 BOM for proper functionality within Victoria 3.