# Victoria 3 Modding Resources

A comprehensive list of all internal Victoria 3 wiki pages related to modding, extracted from the main modding page.

## Navigation & Main Documentation

### [Victoria 3 Wiki](/Victoria_3_Wiki)
Main page of the Victoria 3 wiki with general game information and navigation.

### [Mod](/Mod)
Information about using game mods - covers how to install, enable, and manage mods in Victoria 3.

### [Mod Structure](/Mod_structure)
Detailed documentation on how to structure mod folders and the required metadata.json file format.

### [Mod Files Load Order](/Mod_files_load_order)
Explains how the game prioritizes and loads mod files when multiple mods modify the same content.

### [Console Commands](/Console_commands)
List of debug console commands available when running the game in debug mode.

## Core Modding Articles

### [New Country Modding](/New_country_modding)
Guide for creating entirely new countries, including required files and setup procedures.

### [Event Modding](/Event_modding)
How to create and modify events - the scripted story elements that drive gameplay.

### [Journal Modding](/Journal_modding)
Modifying the journal system that tracks ongoing national objectives and storylines.

### [Modifier Modding](/Modifier_modding)
Creating and editing modifiers that affect various game mechanics and statistics.

### [Flag Modding](/Flag_modding)
How to create and implement custom flags for countries, including file formats and naming conventions.

### [Model Modding](/Model_modding)
Guide for creating and implementing 3D models in the game, including buildings and units.

### [Goods Modding](/Goods_modding)
Creating new trade goods and modifying existing ones, including economic properties.

### [Building Modding](/Building_modding)
How to create new buildings and modify existing ones, including production methods.

## Technical Documentation

### [Defines](/Defines)
Game constants and numerical values that control core game mechanics.

### [Effects](/Effect)
Documentation of all available effects that can be used in events, decisions, and other scripted content.

### [Triggers](/Trigger)
Conditional statements used to determine when events fire or decisions are available.

### [Event Targets](/Event_target)
System for referencing specific game objects (countries, characters, etc.) in events and effects.

### [Scopes](/Scope)
How the game determines the context (country, character, state, etc.) for executing effects and checking triggers.

### [Modifier Types](/Modifier_types)
Complete list of all available modifier types that can affect game mechanics.

### [Data Types](/Data_types)
Documentation of the various data types used in Victoria 3 scripting.

### [Localization](/Localization)
How to create and modify text displayed in the game interface, including multiple language support.

### [Customizable Localization](/Customizable_localization)
Advanced localization features for dynamic text generation based on game state.

## Scripted Content Types

### [Decision Modding](/Decision_modding)
Creating decisions that players can make to affect their country's development.

### [Diplomacy Modding](/Diplomacy_modding)
Modifying diplomatic actions and relationships between countries.

### [History Modding](/History_modding)
Setting up the initial game state, including country borders, technologies, and populations.

### [On Actions](/On_actions)
Events that trigger automatically based on specific game conditions or player actions.

### [Scripted GUI](/Scripted_gui)
Creating custom user interface elements and menus.

### [Script Values](/Script_value)
Reusable calculations and formulas that can be referenced throughout mod files.

## Game Object Types

### [Character Modding](/Character_modding)
Creating and modifying historical and generated characters, including traits and roles.

### [Country Modding](/Country_modding)
General country modification (different from new country creation).

### [Culture Modding](/Culture_modding)
Creating new cultures and modifying cultural traits and behaviors.

### [Decree Modding](/Decree_modding)
Modifying the decree system that allows temporary policy changes.

### [Institution Modding](/Institution_modding)
Creating and modifying government institutions that affect country capabilities.

### [Interest Group Modding](/Interest_group_modding)
Modifying political interest groups and their behaviors and demands.

### [Law Modding](/Law_modding)
Creating and modifying laws that govern how countries function.

### [Pop Modding](/Pop_modding)
Modifying population mechanics, including pop types and behaviors.

### [Power Bloc Modding](/Power_bloc_modding)
Modifying the diplomatic power bloc system introduced in recent updates.

### [Religion Modding](/Religion_modding)
Creating new religions and modifying religious mechanics.

### [Technology Modding](/Technology_modding)
Creating new technologies and modifying the research system.

## Map & Visual Content

### [Map Modding](/Map_modding)
Modifying the game map, including provinces, terrain, and geographical features.

### [State Modding](/State_modding)
Creating and modifying states (administrative regions) within the game.

### [Interface Modding](/Interface_modding)
Customizing the game's user interface elements and layouts.

### [Graphical Asset Modding](/Graphical_asset_modding)
Working with images, textures, and other visual assets.

### [Sound Modding](/Sound_modding)
Adding or modifying audio content including sound effects.

## Specialized Guides

### [Mod Translation](/Mod_translation)
Guide for translating mods into different languages.

### [New State Modding](/New_state_modding)
Specific guide for creating entirely new states on the map.

### [Save-game Editing](/Save-game_editing)
How to modify existing save files for testing or customization.

### [Scripted Function Modding](/Scripted_function_modding)
Creating reusable scripted functions for complex mod logic.

### [Grester's Compendium](/User:Gr3st3r)
Community-created comprehensive modding guide with advanced techniques and examples.

## Technical References

### [Checksum](/Checksum)
Information about game checksums and how they affect multiplayer compatibility.

### [Categories: Timeless](/Category:Timeless)
Pages that remain accurate across different game versions.

## Wiki Utility Pages

### [Contact Us](/Contact_us)
How to contact the wiki administrators for support or contributions.

### [Style Guidelines](/Victoria_3_Wiki:Style)
Wiki formatting and style guidelines for contributors.

### [Recent Changes](/Special:RecentChanges)
List of recent edits and updates to the wiki.

### [Random Page](/Special:Random)
Navigates to a random wiki page.

### [What Links Here](/Special:WhatLinksHere/Modding)
Shows all pages that link to the main modding page.

### [Related Changes](/Special:RecentChangesLinked/Modding)
Shows recent changes to pages linked from the modding page.

---

*Generated from: https://vic3.paradoxwikis.com/Modding*  
*Date: 2025-06-26*

**Note**: All links are relative to the Victoria 3 wiki base URL (https://vic3.paradoxwikis.com). Some pages may be stubs or works in progress.